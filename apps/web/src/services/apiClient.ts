// Centralized API client for ModernAction.io
// Uses axios for robust HTTP client features like interceptors and error handling

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  Campaign,
  CampaignCreate,
  CampaignUpdate,
  CampaignSearchParams,
  Bill,
  BillCreate,
  Bill<PERSON>p<PERSON>,
  BillSearchParams,
  Official,
  OfficialCreate,
  OfficialUpdate,
  OfficialSearchParams,
  Action,
  PaginationParams,
  BillValuesAnalysisResponse,
  BillValuesTagsResponse,
  BillTimelineResponse
} from '../types';

// CRITICAL FIX: Use different URLs for server-side vs client-side requests
// Server-side requests (from Next.js server) should use internal service discovery
// Client-side requests (from browser) should use public ALB URL
const isServer = typeof window === 'undefined';

// Use environment variable with fallback to localhost for development
const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

console.log('API Client Configuration:', { isServer, apiUrl, env: process.env.NEXT_PUBLIC_API_URL, buildTime: new Date().toISOString() });

// Create axios instance with base configuration
// Increased timeout for internal service discovery DNS resolution
const apiClient: AxiosInstance = axios.create({
  baseURL: apiUrl,
  timeout: 120000, // 120 seconds - increased for admin operations and bill processing
  // Don't set Content-Type by default to avoid CORS preflight issues
});

// Create a separate instance for AI operations with longer timeout
const aiApiClient: AxiosInstance = axios.create({
  baseURL: apiUrl,
  timeout: 180000, // 180 seconds (3 minutes) for AI operations and bill processing
  // Don't set Content-Type by default to avoid CORS preflight issues
});

// Auth token storage for client-side requests
let authToken: string | null = null;

export const setAuthToken = (token: string | null) => {
  authToken = token;
};

export const getAuthToken = () => authToken;

// Request interceptor for adding auth tokens and content-type
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token when available
    const token = getAuthToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add Content-Type header only for requests that send data
    if (config.method && ['post', 'put', 'patch'].includes(config.method.toLowerCase())) {
      config.headers['Content-Type'] = 'application/json';
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    // Handle common error scenarios
    if (error.response?.status === 401) {
      // Handle unauthorized access
      console.error('Unauthorized access - redirecting to login');
      // Redirect to login page
    } else if (error.response?.status === 404) {
      // Handle not found errors
      console.error('Resource not found:', error.response.data);
    } else if (error.response?.status >= 500) {
      // Handle server errors
      console.error('Server error:', error.response.data);
    }

    return Promise.reject(error);
  }
);

// Helper function to build query string from parameters
const buildQueryString = (params: Record<string, unknown>): string => {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, value.toString());
    }
  });

  return searchParams.toString();
};

// Campaign API functions
export const campaignApi = {
  // Get all campaigns with pagination
  getCampaigns: async (params: PaginationParams = {}): Promise<Campaign[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Campaign[]>(`/campaigns/?${queryString}`);
    return response.data;
  },

  // Get single campaign by ID
  getCampaignById: async (id: string): Promise<Campaign> => {
    const response = await apiClient.get<Campaign>(`/campaigns/${id}`);
    return response.data;
  },

  // Create new campaign
  createCampaign: async (campaignData: CampaignCreate): Promise<Campaign> => {
    const response = await apiClient.post<Campaign>('/campaigns/', campaignData);
    return response.data;
  },

  // Update existing campaign
  updateCampaign: async (id: string, campaignData: CampaignUpdate): Promise<Campaign> => {
    const response = await apiClient.put<Campaign>(`/campaigns/${id}`, campaignData);
    return response.data;
  },

  // Delete campaign
  deleteCampaign: async (id: string): Promise<void> => {
    await apiClient.delete(`/campaigns/${id}`);
  },

  // Search campaigns with filters
  searchCampaigns: async (params: CampaignSearchParams = {}): Promise<Campaign[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Campaign[]>(`/campaigns/search?${queryString}`);
    return response.data;
  },

  // Get featured campaigns
  getFeaturedCampaigns: async (params: PaginationParams = {}): Promise<Campaign[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Campaign[]>(`/campaigns/featured?${queryString}`);
    return response.data;
  },

  // Get active campaigns
  getActiveCampaigns: async (params: PaginationParams = {}): Promise<Campaign[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Campaign[]>(`/campaigns/active?${queryString}`);
    return response.data;
  },

  // Get campaigns by status
  getCampaignsByStatus: async (status: string, params: PaginationParams = {}): Promise<Campaign[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Campaign[]>(`/campaigns/status/${status}?${queryString}`);
    return response.data;
  },

  // Get campaigns by bill ID
  getCampaignsByBill: async (billId: string, params: PaginationParams = {}): Promise<Campaign[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Campaign[]>(`/campaigns/bill/${billId}?${queryString}`);
    return response.data;
  },
};

// Bill API functions
export const billApi = {
  // Get all bills with pagination
  getBills: async (params: PaginationParams = {}): Promise<Bill[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Bill[]>(`/bills/?${queryString}`);
    return response.data;
  },

  // Get bills using simple endpoint (faster, less data)
  getBillsSimple: async (params: PaginationParams = {}): Promise<Bill[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Bill[]>(`/bills/simple?${queryString}`);
    return response.data;
  },

  // Get single bill by ID
  getBillById: async (id: string): Promise<Bill> => {
    const response = await apiClient.get<Bill>(`/bills/${id}`);
    return response.data;
  },

  // Create new bill
  createBill: async (billData: BillCreate): Promise<Bill> => {
    const response = await apiClient.post<Bill>('/bills/', billData);
    return response.data;
  },

  // Update existing bill
  updateBill: async (id: string, billData: BillUpdate): Promise<Bill> => {
    const response = await apiClient.put<Bill>(`/bills/${id}`, billData);
    return response.data;
  },

  // Delete bill
  deleteBill: async (id: string): Promise<void> => {
    await apiClient.delete(`/bills/${id}`);
  },

  // Search bills with filters
  searchBills: async (params: BillSearchParams = {}): Promise<Bill[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Bill[]>(`/bills/search?${queryString}`);
    return response.data;
  },

  // Get featured bills
  getFeaturedBills: async (params: PaginationParams = {}): Promise<Bill[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Bill[]>(`/bills/featured?${queryString}`);
    return response.data;
  },

  // Get bills by status
  getBillsByStatus: async (status: string, params: PaginationParams = {}): Promise<Bill[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Bill[]>(`/bills/status/${status}?${queryString}`);
    return response.data;
  },

  // Get bill summary versions
  getBillSummaryVersions: async (billId: string, includeContent: boolean = false): Promise<Record<string, unknown>[]> => {
    const response = await apiClient.get<Record<string, unknown>[]>(`/bills/${billId}/summary-versions?include_content=${includeContent}`);
    return response.data;
  },

  // Get bill status history
  getBillStatusHistory: async (billId: string): Promise<Record<string, unknown>[]> => {
    const response = await apiClient.get<Record<string, unknown>[]>(`/bills/${billId}/status-history`);
    return response.data;
  },

  // Get complete bill timeline
  getBillTimeline: async (billId: string, includeSummaryContent: boolean = false): Promise<BillTimelineResponse> => {
    const response = await apiClient.get<BillTimelineResponse>(`/bills/${billId}/timeline?include_summary_content=${includeSummaryContent}`);
    return response.data;
  },

  // Get values analysis for a bill
  getBillValuesAnalysis: async (billId: string): Promise<BillValuesAnalysisResponse['analysis']> => {
    const response = await apiClient.get<BillValuesAnalysisResponse>(`/values/bill/${billId}`);
    return response.data.analysis;
  },

  // Get values tags for a bill
  getBillValuesTags: async (billId: string): Promise<BillValuesTagsResponse['tags']> => {
    const response = await apiClient.get<BillValuesTagsResponse>(`/values/bill/${billId}/tags`);
    return response.data.tags;
  },

  // Get values analysis statistics
  getValuesAnalysisStats: async (): Promise<Record<string, unknown>> => {
    const response = await apiClient.get<Record<string, unknown>>('/values/stats');
    return response.data;
  },

  // Bill details API
  getBillDetailsBySlug: async (slug: string) => {
    const response = await apiClient.get('/bills/details/by-slug/' + encodeURIComponent(slug));
    return response.data;
  },
};

// Official API functions
export const officialApi = {
  getBillDetailsByBillId: async (billId: string) => {
    const response = await apiClient.get('/bills/' + encodeURIComponent(billId) + '/details');
    return response.data;
  },

  // Get all officials with pagination
  getOfficials: async (params: PaginationParams = {}): Promise<Official[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Official[]>(`/officials/?${queryString}`);
    return response.data;
  },

  // Get single official by ID
  getOfficialById: async (id: string): Promise<Official> => {
    try {
      // Use a shorter timeout for individual official lookups
      const response = await apiClient.get<Official>(`/officials/${id}`, {
        timeout: 30000, // 30 seconds - shorter timeout for single record
      });
      return response.data;
    } catch (error) {
      console.error(`Failed to fetch official ${id}:`, error);
      // Re-throw with more context
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          throw new Error(`Request timeout: Official ${id} took too long to load`);
        }
        if (error.response?.status === 404) {
          throw new Error(`Official ${id} not found`);
        }
        if (error.response?.status >= 500) {
          throw new Error(`Server error when fetching official ${id}`);
        }
      }
      throw error;
    }
  },

  // Create new official
  createOfficial: async (officialData: OfficialCreate): Promise<Official> => {
    const response = await apiClient.post<Official>('/officials/', officialData);
    return response.data;
  },

  // Update existing official
  updateOfficial: async (id: string, officialData: OfficialUpdate): Promise<Official> => {
    const response = await apiClient.put<Official>(`/officials/${id}`, officialData);
    return response.data;
  },

  // Delete official
  deleteOfficial: async (id: string): Promise<void> => {
    await apiClient.delete(`/officials/${id}`);
  },

  // Search officials with filters
  searchOfficials: async (params: OfficialSearchParams = {}): Promise<Official[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Official[]>(`/officials/search?${queryString}`);
    return response.data;
  },

  // Get officials by zip code (Enhanced endpoint)
  getOfficialsByZip: async (zipCode: string, params: PaginationParams = {}): Promise<Official[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Official[]>(`/officials/by-zip/${zipCode}?${queryString}`);
    return response.data;
  },

  // Get officials by government level
  getOfficialsByLevel: async (level: string, params: PaginationParams = {}): Promise<Official[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Official[]>(`/officials/levels/${level}?${queryString}`);
    return response.data;
  },

  // Get officials by chamber
  getOfficialsByChamber: async (chamber: string, params: PaginationParams = {}): Promise<Official[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Official[]>(`/officials/chambers/${chamber}?${queryString}`);
    return response.data;
  },

  // Get official by external ID (bioguide, openstates, etc.)
  getOfficialByExternalId: async (idType: string, externalId: string): Promise<Official> => {
    const response = await apiClient.get<Official>(`/officials/external/${idType}/${externalId}`);
    return response.data;
  },
};

// Action API interface for creating actions
export interface ActionCreateRequest {
  subject: string;
  message: string;
  action_type?: string;
  user_name: string;
  user_email: string;
  user_address?: string;
  user_zip_code?: string;
  campaign_id: string;
  official_id: string;
  contact_email?: string;
  contact_phone?: string;
  contact_address?: string;
}

// Bill Action interfaces for new Part 2 system
export interface BillActionData {
  id: string;
  bill_id: string;
  seo_slug: string;
  seo_title: string;
  hero_summary: string;
  positions: {
    support_reasons: Array<{ claim: string; citations: any[]; justification: string }>;
    oppose_reasons: Array<{ claim: string; citations: any[]; justification: string }>;
    amend_reasons: Array<{ claim: string; citations: any[]; justification: string }>;
  };
  message_templates: {
    support: string;
    oppose: string;
    amend: string;
  };
  tags: string[];
}

export interface BillActionSubmitRequest {
  bill_id: string;
  stance: 'support' | 'oppose' | 'amend';
  selected_reasons: string[];
  custom_reasons?: string[];
  personal_stories?: string;
  custom_message?: string;
  first_name: string;
  last_name: string;
  zip_code: string;
  email: string;
  address?: string;
  city?: string;
  state?: string;
  target_chamber?: string; // 'house', 'senate', or 'both' - for Action Network form routing
}

export interface MessagePreviewRequest {
  bill_id: string;
  stance: 'support' | 'oppose' | 'amend';
  selected_reasons: string[];
  custom_reasons?: string[];
  personal_stories?: string;
  first_name: string;
  last_name: string;
  zip_code: string;
}

export interface MessagePreviewResponse {
  success: boolean;
  bill: {
    id: string;
    title: string;
    bill_number: string;
  };
  representatives: Record<string, unknown>[];
  personalized_messages: Record<string, unknown>[];
  stance: string;
  selected_reasons: string[];
  custom_reasons: string[];
}

export interface BillActionSubmitResponse {
  success: boolean;
  message: string;
  action_network_embed?: {
    campaign_id: string;
    chamber: string;
    embed_url: string;
    iframe_url: string;
    status: string;
  };
  delivery_summary: {
    method: string;
    requires_user_completion?: boolean;
    total_targets: number;
    message: string;
  };
  officials_contacted: number;
  representatives: string[];
  personalized_message_preview: string;
  database_records: {
    actions_created: number;
    campaign_id: string;
    user_id: string;
  };
  user_instructions?: {
    next_step: string;
    form_url: string;
    note: string;
  };
  // Legacy fields for backward compatibility
  bill?: {
    id: string;
    number: string;
    title: string;
  };
  officials_lookup?: {
    status: string;
    total_representatives: number;
    representatives: string[];
  };
  message_personalization?: {
    status: string;
    total_messages: number;
    sample_subject?: string;
  };
  action_network?: {
    status: string;
    total_submissions?: number;
    submissions?: Array<{
      status: string;
      message_id: string;
      representative: string;
      subject: string;
    }>;
    note?: string;
  };
}

export interface OfficialsLookupResponse {
  status: string;
  zip_code: string;
  state: string;
  district?: string;
  senators: Array<{
    full_name: string;
    first_name: string;
    last_name: string;
    title: string;
    chamber: string;
    state: string;
    party: string;
    email?: string;
    phone?: string;
    website?: string;
  }>;
  representative?: {
    full_name: string;
    first_name: string;
    last_name: string;
    title: string;
    chamber: string;
    state: string;
    party: string;
    email?: string;
    phone?: string;
    website?: string;
  };
  total_representatives: number;
  note?: string;
}

export interface ActionCreateResponse {
  id: string;
  subject: string;
  message: string;
  action_type: string;
  status: string;
  user_name: string;
  user_email: string;
  campaign_id: string;
  official_id: string;
  created_at: string;
  updated_at: string;
}

// Action API functions
export const actionApi = {
  // Create new action
  createAction: async (actionData: ActionCreateRequest): Promise<ActionCreateResponse> => {
    const response = await apiClient.post<ActionCreateResponse>('/actions/', actionData);
    return response.data;
  },

  // Get single action by ID
  getActionById: async (id: string): Promise<Action> => {
    const response = await apiClient.get<Action>(`/actions/${id}`);
    return response.data;
  },

  // Get actions with filters
  getActions: async (params: Record<string, unknown> = {}): Promise<Action[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Action[]>(`/actions/?${queryString}`);
    return response.data;
  },

  // Retry failed action
  retryAction: async (id: string): Promise<ActionCreateResponse> => {
    const response = await apiClient.post<ActionCreateResponse>(`/actions/${id}/retry`);
    return response.data;
  },

  // Delete action
  deleteAction: async (id: string): Promise<void> => {
    await apiClient.delete(`/actions/${id}`);
  },

  // Get action statistics
  getActionStats: async (campaignId?: string): Promise<Record<string, unknown>> => {
    const url = campaignId ? `/actions/stats?campaign_id=${campaignId}` : '/actions/stats';
    const response = await apiClient.get(url);
    return response.data;
  },

  // Get user actions
  getUserActions: async (userId: string, params: PaginationParams = {}): Promise<Action[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Action[]>(`/actions/user/${userId}?${queryString}`);
    return response.data;
  },

  // Get campaign actions
  getCampaignActions: async (campaignId: string, params: PaginationParams = {}): Promise<Action[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Action[]>(`/actions/campaign/${campaignId}/stats?${queryString}`);
    return response.data;
  },
};

// Bill Action API functions for Part 2 integration
export const billActionApi = {
  // Get bill action data (AI summary, reasons, templates)
  getBillActionData: async (billId: string): Promise<BillActionData> => {
    const response = await apiClient.get<BillActionData>(`/bills/${billId}/action-data`);
    return response.data;
  },

  // Preview personalized message before submission (uses longer timeout)
  previewMessage: async (previewData: MessagePreviewRequest): Promise<MessagePreviewResponse> => {
    const response = await aiApiClient.post<MessagePreviewResponse>('/actions/preview-message', previewData);
    return response.data;
  },

  // Submit bill action (complete flow: officials lookup + message personalization + submission)
  submitBillAction: async (actionData: BillActionSubmitRequest): Promise<BillActionSubmitResponse> => {
    // Use development endpoint for testing (bypasses authentication)
    const endpoint = process.env.NODE_ENV === 'development' ? '/actions/submit-dev' : '/actions/submit';
    const response = await apiClient.post<BillActionSubmitResponse>(endpoint, actionData);
    return response.data;
  },

  // Lookup officials by ZIP code
  lookupOfficials: async (zipCode: string): Promise<OfficialsLookupResponse> => {
    const response = await apiClient.get<OfficialsLookupResponse>(`/officials/lookup?zip_code=${zipCode}`);
    return response.data;
  },
};

// User Profile Data Types
export interface UserProfile {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  zip_code?: string;
  phone_number?: string;
  address?: string;
  city?: string;
  state?: string;
  email_notifications: boolean;
  sms_notifications: boolean;
  save_address_for_future_actions: boolean;
  bio?: string;
  profile_picture_url?: string;
  is_active: boolean;
  is_verified: boolean;
  is_superuser: boolean;
  created_at: string;
  updated_at: string;
  last_login_at?: string;
}

export interface UserProfileUpdate {
  first_name?: string;
  last_name?: string;
  zip_code?: string;
  phone_number?: string;
  address?: string;
  city?: string;
  state?: string;
  email_notifications?: boolean;
  sms_notifications?: boolean;
  save_address_for_future_actions?: boolean;
  bio?: string;
  profile_picture_url?: string;
}

// User API functions
export const userApi = {
  // Get current user profile
  getProfile: async (accessToken?: string): Promise<UserProfile> => {
    const headers = accessToken ? { Authorization: `Bearer ${accessToken}` } : {};
    const response = await apiClient.get<UserProfile>('/users/profile', { headers });
    return response.data;
  },

  // Update user profile
  updateProfile: async (profileData: UserProfileUpdate, accessToken?: string): Promise<UserProfile> => {
    const headers = accessToken ? { Authorization: `Bearer ${accessToken}` } : {};
    const response = await apiClient.patch<UserProfile>('/users/profile', profileData, { headers });
    return response.data;
  },

  // Delete saved address data
  deleteSavedAddress: async (accessToken?: string): Promise<{ message: string }> => {
    const headers = accessToken ? { Authorization: `Bearer ${accessToken}` } : {};
    const response = await apiClient.delete<{ message: string }>('/users/profile/address', { headers });
    return response.data;
  },
};

// Export the configured axios instance for custom requests
export default apiClient;
