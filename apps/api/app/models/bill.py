# app/models/bill.py
from sqlalchemy import Column, String, Text, DateTime, <PERSON>olean, Inte<PERSON>, Enum as SQLE<PERSON>, ForeignKey
from sqlalchemy.dialects import postgresql
from datetime import datetime
from sqlalchemy.orm import relationship
from app.db.base_class import Base
import enum

class BillStatus(str, enum.Enum):
    DRAFT = "draft"
    INTRODUCED = "introduced"
    COMMITTEE = "committee"
    FLOOR = "floor"
    PASSED = "passed"
    SIGNED = "signed"
    VETOED = "vetoed"
    FAILED = "failed"

class BillType(str, enum.Enum):
    HOUSE_BILL = "house_bill"
    SENATE_BILL = "senate_bill"
    HOUSE_RESOLUTION = "house_resolution"
    SENATE_RESOLUTION = "senate_resolution"
    JOINT_RESOLUTION = "joint_resolution"
    CONCURRENT_RESOLUTION = "concurrent_resolution"

class Bill(Base):
    __tablename__ = "bills"

    # Basic bill information
    title = Column(String, nullable=False, index=True)
    description = Column(Text, nullable=True)
    bill_number = Column(String, nullable=False, index=True)

    # Bill classification
    bill_type = Column(SQLEnum(BillType), nullable=False)
    status = Column(SQLEnum(BillStatus), nullable=False, default=BillStatus.INTRODUCED)

    # Legislative session information
    session_year = Column(Integer, nullable=False)
    chamber = Column(String, nullable=False)  # "house" or "senate"
    state = Column(String, nullable=False, default="federal")  # state abbreviation or "federal"

    # Bill content
    full_text = Column(Text, nullable=True)
    summary = Column(Text, nullable=True)
    ai_summary = Column(Text, nullable=True)  # AI-generated summary (legacy)
    tldr = Column(Text, nullable=True)  # TL;DR - Ultra-simple explanation in plain English for 8th grade reading level

    # Structured AI summary fields (PostgreSQL JSONB)
    summary_what_does = Column(postgresql.JSONB, nullable=True)
    summary_who_affects = Column(postgresql.JSONB, nullable=True)
    summary_why_matters = Column(postgresql.JSONB, nullable=True)
    summary_key_provisions = Column(postgresql.JSONB, nullable=True)
    summary_timeline = Column(postgresql.JSONB, nullable=True)
    summary_cost_impact = Column(postgresql.JSONB, nullable=True)

    # AI-generated analysis fields (Sprint A requirements)
    reasons_for_support = Column(Text, nullable=True)  # AI-generated arguments in favor
    reasons_for_opposition = Column(Text, nullable=True)  # AI-generated arguments against

    # Enhanced AI processing fields
    support_reasons = Column(postgresql.JSONB, nullable=True)  # JSONB array of support reasons
    oppose_reasons = Column(postgresql.JSONB, nullable=True)  # JSONB array of oppose reasons
    amend_reasons = Column(postgresql.JSONB, nullable=True)  # JSONB array of amend reasons
    message_templates = Column(postgresql.JSONB, nullable=True)  # JSONB object with message templates
    ai_tags = Column(postgresql.JSONB, nullable=True)  # JSONB array of AI-generated tags
    ai_processed_at = Column(DateTime, nullable=True)  # When AI processing was completed

    # Environmental and social impact analysis
    environmental_threat_analysis = Column(postgresql.JSONB, nullable=True)  # Analysis of environmental threats
    social_rights_threat_analysis = Column(postgresql.JSONB, nullable=True)  # Analysis of social rights threats
    environmental_justice_threat_analysis = Column(postgresql.JSONB, nullable=True)  # Analysis of environmental justice threats

    # External references
    openstates_id = Column(String, nullable=True, unique=True, index=True)
    congress_gov_id = Column(String, nullable=True, unique=True, index=True)

    # URLs and links
    source_url = Column(String, nullable=True)
    text_url = Column(String, nullable=True)
    official_bill_url = Column(String, nullable=True)  # Official Congress.gov or state legislature URL

    # Dates
    introduced_date = Column(DateTime, nullable=True)
    last_action_date = Column(DateTime, nullable=True)

    # Sponsorship information
    sponsor_name = Column(String, nullable=True)
    sponsor_party = Column(String, nullable=True)
    sponsor_state = Column(String, nullable=True)

    # Co-sponsors and vote information
    cosponsors = Column(Text, nullable=True)  # Array of cosponsor information
    vote_history = Column(Text, nullable=True)  # Array of vote records

    # Tracking and engagement
    is_featured = Column(Boolean, default=False, nullable=False)
    priority_score = Column(Integer, default=0, nullable=False)

    # Tags and categories
    tags = Column(Text, nullable=True)  # Array of tags
    categories = Column(Text, nullable=True)  # Array of categories

    # Metadata
    bill_metadata = Column(Text, nullable=True)  # Additional metadata

    # Relationships
    campaigns = relationship("Campaign", back_populates="bill", cascade="all, delete-orphan")
    status_history = relationship("BillStatusPipeline", back_populates="bill", cascade="all, delete-orphan", order_by="BillStatusPipeline.detected_at.desc()")
    summary_versions = relationship("BillSummaryVersion", back_populates="bill", cascade="all, delete-orphan", order_by="BillSummaryVersion.version_number.desc()")
    values_analysis = relationship("BillValuesAnalysis", back_populates="bill", uselist=False, cascade="all, delete-orphan")
    actions = relationship("Action", back_populates="bill")
    reasoning_options = relationship("ReasoningOption", back_populates="bill", cascade="all, delete-orphan")
    ai_usage_logs = relationship("AIUsageLog", back_populates="bill", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Bill(number='{self.bill_number}', title='{self.title[:50]}...')>"

    @property
    def full_bill_id(self) -> str:
        return f"{self.state.upper()}-{self.bill_number}-{self.session_year}"


class BillStatusPipeline(Base):
    """
    Model for tracking bill status changes over time.

    This table maintains a history of all status changes for bills,
    enabling us to track the legislative progress and notify users
    when significant changes occur.
    """
    __tablename__ = "bill_status_pipeline"

    # Foreign key to the bill
    bill_id = Column(String(255), ForeignKey("bills.id"), nullable=False, index=True)

    # Status information
    previous_status = Column(SQLEnum(BillStatus), nullable=True)  # Previous status (null for first entry)
    current_status = Column(SQLEnum(BillStatus), nullable=False, index=True)

    # Change metadata
    status_changed_at = Column(DateTime, nullable=False)  # When the status actually changed
    detected_at = Column(DateTime, nullable=False)  # When our system detected the change

    # External API data
    external_data = Column(Text, nullable=True)  # Raw data from OpenStates API
    vote_details = Column(Text, nullable=True)  # Specific vote information if available

    # Processing flags
    notification_sent = Column(Boolean, default=False, nullable=False)  # Whether users were notified
    is_significant_change = Column(Boolean, default=False, nullable=False)  # Whether this warrants notification

    # Additional context
    notes = Column(Text, nullable=True)  # Any additional notes about the status change

    # Relationships
    bill = relationship("Bill", back_populates="status_history")

    def __repr__(self):
        return f"<BillStatusPipeline(bill_id='{self.bill_id}', {self.previous_status} -> {self.current_status})>"

    @property
    def is_final_status(self) -> bool:
        """Check if this status represents a final outcome"""
        return self.current_status in [BillStatus.PASSED, BillStatus.SIGNED, BillStatus.VETOED, BillStatus.FAILED]


class BillSummaryVersion(Base):
    """
    Model for tracking bill summary changes over time.

    This table maintains a history of all summary changes for bills,
    enabling us to track how AI-generated summaries evolve as bills
    are amended or updated.
    """
    __tablename__ = "bill_summary_versions"

    # Foreign key to the bill
    bill_id = Column(String(255), ForeignKey("bills.id"), nullable=False, index=True)

    # Version metadata
    version_number = Column(Integer, nullable=False)  # Sequential version number starting from 1
    change_reason = Column(String, nullable=True)  # Why the summary was updated (e.g., "bill_amended", "ai_improvement")

    # Summary content snapshots (PostgreSQL JSONB)
    summary_what_does = Column(postgresql.JSONB, nullable=True)
    summary_who_affects = Column(postgresql.JSONB, nullable=True)
    summary_why_matters = Column(postgresql.JSONB, nullable=True)
    summary_key_provisions = Column(postgresql.JSONB, nullable=True)
    summary_timeline = Column(postgresql.JSONB, nullable=True)
    summary_cost_impact = Column(postgresql.JSONB, nullable=True)

    # Legacy text summaries
    ai_summary = Column(Text, nullable=True)
    tldr = Column(Text, nullable=True)

    # Processing metadata
    ai_processed_at = Column(DateTime, nullable=True)  # When this version was generated
    is_current = Column(Boolean, default=False, nullable=False)  # Whether this is the current version

    # Change tracking
    changes_detected = Column(postgresql.JSONB, nullable=True)  # What fields changed from previous version

    # Relationships
    bill = relationship("Bill", back_populates="summary_versions")

    def __repr__(self):
        return f"<BillSummaryVersion(bill_id='{self.bill_id}', version={self.version_number}, current={self.is_current})>"

    @property
    def has_structured_summary(self) -> bool:
        """Check if this version has structured JSONB summary data"""
        return any([
            self.summary_what_does,
            self.summary_who_affects,
            self.summary_why_matters,
            self.summary_key_provisions,
            self.summary_timeline,
            self.summary_cost_impact
        ])
